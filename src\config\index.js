/**
 * 项目配置文件 - 统一配置管理
 */

// 画布相关配置
export const CANVAS_CONFIG = {
  HEADER_HEIGHT: 200, // 头部和底部面板的总高度
  BACKGROUND_COLOR: '#e0e0e0',
};

// 颜色配置 - 统一管理所有颜色
export const COLORS = {
  // UI颜色
  HEADER_BG: '#222',
  HEADER_TEXT: '#fff',
  PANEL_BG: '#333',
  
  // 飞机颜色
  AIRCRAFT: {
    HEAVY: '#ff6b6b',
    MEDIUM: '#4ecdc4', 
    LIGHT: '#45b7d1',
    DEFAULT: '#333'
  },
  
  // 节点颜色
  NODES: {
    RUNWAY: '#FF9500',
    GATE: '#87CEFA',
    TAXIWAY: '#00CC00',
    START: '#00FF00',
    END: '#FF5555'
  }
};

// 跑道类型
export const RUNWAY_TYPES = {
  SIMPLE: 'simple',
  MEDIUM: 'medium',
  COMPLEX: 'complex',
};

// 动画相关
export const ANIMATION = {
  FRAME_TIME: 16.67, // 60fps的帧时间（毫秒）
};

// 物理配置
export const PHYSICS_CONFIG = {
  // 速度相关配置
  SPEED: {
    MIN: 0,     // 最小速度（千米/时）
    MAX: 20,    // 最大速度（千米/时）
    TURN: 10    // 转弯速度（千米/时）
  },

  // 加速度相关配置
  ACCELERATION: {
    NORMAL: 5,  // 正常加速度（千米/时/秒）
    BRAKE: 10,  // 制动减速度（千米/时/秒）
    TURN: 2     // 转弯加速度（千米/时/秒）
  },

  // 距离相关配置
  DISTANCE: {
    TURN_PREDICTION: 20, // 转弯预测距离（米）
    NODE_RADIUS: 15      // 节点判定半径（米）
  },

  // 角度相关配置
  ANGLE: {
    TURN_THRESHOLD: 0.2  // 转弯阈值（弧度）
  },

  // 转换系数
  CONVERSION: {
    KMH_TO_MS: 1/3.6,    // 千米/时 转 米/秒
    MS_TO_KMH: 3.6,      // 米/秒 转 千米/时
    DEG_TO_RAD: Math.PI / 180,  // 角度转弧度
    RAD_TO_DEG: 180 / Math.PI   // 弧度转角度
  }
}; 