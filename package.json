{"name": "airport-simulation", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "core-js": "^3.8.3", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vue": "^3.2.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.17.0", "vite": "^5.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}