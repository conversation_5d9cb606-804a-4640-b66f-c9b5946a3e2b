import { COLORS } from '@/config';

/**
 * 跑道渲染器类
 */
export class RunwayRenderer {
  constructor(ctx) {
    this.ctx = ctx;
  }

  /**
   * 绘制整个机场地图
   * @param {Object} transformedNodes - 转换后的所有节点
   * @param {Object} edges - 所有边
   */
  renderAirportMap(transformedNodes, edges) {
    this.ctx.save();
    
    // 绘制所有边（跑道和滑行道）
    Object.values(edges).forEach(edge => {
      const sourceNode = transformedNodes[edge.source];
      const targetNode = transformedNodes[edge.target];
      
      if (sourceNode && targetNode) {
        // 判断是否为跑道或滑行道
        const isRunway = sourceNode.specification === 'runway' || 
                        targetNode.specification === 'runway';
        
        // 设置跑道/滑行道样式
        this.ctx.beginPath();
        this.ctx.moveTo(sourceNode.canvasX, sourceNode.canvasY);
        this.ctx.lineTo(targetNode.canvasX, targetNode.canvasY);
        
        if (isRunway) {
          // 跑道样式 - 宽大的深灰色
          this.ctx.lineWidth = 20;
          this.ctx.strokeStyle = '#555555';
          this.ctx.stroke();
          
          // 中心线 - 虚线白色
          this.ctx.beginPath();
          this.ctx.moveTo(sourceNode.canvasX, sourceNode.canvasY);
          this.ctx.lineTo(targetNode.canvasX, targetNode.canvasY);
          this.ctx.setLineDash([10, 10]);
          this.ctx.lineWidth = 2;
          this.ctx.strokeStyle = '#FFFFFF';
          this.ctx.stroke();
          this.ctx.setLineDash([]);
        } else {
          // 滑行道样式 - 中等宽度的灰色
          this.ctx.lineWidth = 10;
          this.ctx.strokeStyle = '#777777';
          this.ctx.stroke();
          
          // 滑行道边缘线 - 黄色
          this.ctx.beginPath();
          this.ctx.moveTo(sourceNode.canvasX, sourceNode.canvasY);
          this.ctx.lineTo(targetNode.canvasX, targetNode.canvasY);
          this.ctx.lineWidth = 1;
          this.ctx.strokeStyle = '#FFCC00';
          this.ctx.setLineDash([5, 5]);
          this.ctx.stroke();
          this.ctx.setLineDash([]);
        }
      }
    });
    
    // 绘制所有节点（交叉点、登机口等）
    Object.values(transformedNodes).forEach(node => {
      const radius = node.specification === 'runway' ? 7 : 
                    node.specification === 'gate' ? 6 : 0;
                    
      // 绘制节点底层（阴影效果）
      this.ctx.beginPath();
      this.ctx.arc(node.canvasX, node.canvasY, radius + 2, 0, Math.PI * 2);
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
      this.ctx.fill();
      
      // 绘制节点
      this.ctx.beginPath();
      this.ctx.arc(node.canvasX, node.canvasY, radius, 0, Math.PI * 2);
      
      // 根据节点类型使用不同的颜色和样式
      if (node.specification === 'runway') {
        // 跑道起点/终点节点 - 突出显示
        const gradient = this.ctx.createRadialGradient(
          node.canvasX, node.canvasY, 0,
          node.canvasX, node.canvasY, radius
        );
        gradient.addColorStop(0, COLORS.NODES.RUNWAY);
        gradient.addColorStop(1, '#FF4500');
        this.ctx.fillStyle = gradient;
      } else if (node.specification === 'gate') {
        // 登机口节点 - 蓝色渐变
        const gradient = this.ctx.createRadialGradient(
          node.canvasX, node.canvasY, 0,
          node.canvasX, node.canvasY, radius
        );
        gradient.addColorStop(0, COLORS.NODES.GATE);
        gradient.addColorStop(1, '#4169E1');
        this.ctx.fillStyle = gradient;
      } else {
        // 普通节点 - 灰色
        this.ctx.fillStyle = '#555555';
      }
      
      this.ctx.fill();
      this.ctx.strokeStyle = '#333333';
      this.ctx.lineWidth = 1;
      this.ctx.stroke();
      
      // 为特殊节点添加标签
      if (node.specification === 'runway' || node.specification === 'gate') {
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 10px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(node.id, node.canvasX, node.canvasY);
      }
    });
    
    this.ctx.restore();
  }

  /**
   * 绘制选定的路径
   * @param {Array} path - 路径点数组
   * @param {Object} options - 渲染选项
   */
  render(path, options = {}) {
    const {
      pathWidth = 6,
      pathColor = COLORS.NODES.TAXIWAY,
      highlightStart = true,
      highlightEnd = true
    } = options;

    this.ctx.save();

    // 绘制路径主体
    if (path.length > 1) {
      // 路径底层 - 提供阴影效果
      this.ctx.beginPath();
      this.ctx.moveTo(path[0].x, path[0].y);
      
      for (let i = 1; i < path.length; i++) {
        this.ctx.lineTo(path[i].x, path[i].y);
      }
      
      this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
      this.ctx.lineWidth = pathWidth + 2;
      this.ctx.stroke();
      
      // 主路径
      this.ctx.beginPath();
      this.ctx.moveTo(path[0].x, path[0].y);
      
      for (let i = 1; i < path.length; i++) {
        this.ctx.lineTo(path[i].x, path[i].y);
      }
      
      // 创建渐变效果
      if (path.length > 1) {
        const gradient = this.ctx.createLinearGradient(
          path[0].x, path[0].y,
          path[path.length-1].x, path[path.length-1].y
        );
        gradient.addColorStop(0, COLORS.NODES.TAXIWAY);  // 起点颜色
        gradient.addColorStop(1, '#004400');  // 终点颜色
        this.ctx.strokeStyle = gradient;
      } else {
        this.ctx.strokeStyle = pathColor;
      }
      
      this.ctx.lineWidth = pathWidth;
      this.ctx.lineCap = 'round';
      this.ctx.lineJoin = 'round';
      this.ctx.stroke();
      
      // 路径方向指示器 - 箭头
      for (let i = 1; i < path.length; i += 3) {
        const prev = path[i-1];
        const curr = path[i];
        
        // 计算角度和距离
        const angle = Math.atan2(curr.y - prev.y, curr.x - prev.x);
        const distance = Math.sqrt(Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2));
        
        // 如果线段足够长，绘制方向箭头
        if (distance > 50) {
          const midX = (prev.x + curr.x) / 2;
          const midY = (prev.y + curr.y) / 2;
          
          this.ctx.save();
          this.ctx.translate(midX, midY);
          this.ctx.rotate(angle);
          
          // 绘制箭头
          this.ctx.beginPath();
          this.ctx.moveTo(8, 0);
          this.ctx.lineTo(-4, -4);
          this.ctx.lineTo(-2, 0);
          this.ctx.lineTo(-4, 4);
          this.ctx.closePath();
          this.ctx.fillStyle = pathColor;
          this.ctx.fill();
          this.ctx.strokeStyle = '#FFFFFF';
          this.ctx.lineWidth = 1;
          this.ctx.stroke();
          
          this.ctx.restore();
        }
      }
    }

    // 绘制起始点标记
    if (highlightStart && path.length > 0) {
      const startPoint = path[0];
      
      this.ctx.beginPath();
      this.ctx.arc(startPoint.x, startPoint.y, 8, 0, Math.PI * 2);
      
      const startGradient = this.ctx.createRadialGradient(
        startPoint.x, startPoint.y, 0,
        startPoint.x, startPoint.y, 8
      );
      startGradient.addColorStop(0, COLORS.NODES.START);
      startGradient.addColorStop(1, '#008800');
      this.ctx.fillStyle = startGradient;
      this.ctx.fill();
      this.ctx.strokeStyle = '#FFFFFF';
      this.ctx.lineWidth = 2;
      this.ctx.stroke();
      
      // 起始点文字标记
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.font = 'bold 10px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText('S', startPoint.x, startPoint.y);
    }

    // 绘制终点标记
    if (highlightEnd && path.length > 1) {
      const endPoint = path[path.length - 1];
      
      this.ctx.beginPath();
      this.ctx.arc(endPoint.x, endPoint.y, 8, 0, Math.PI * 2);
      
      const endGradient = this.ctx.createRadialGradient(
        endPoint.x, endPoint.y, 0,
        endPoint.x, endPoint.y, 8
      );
      endGradient.addColorStop(0, COLORS.NODES.END);
      endGradient.addColorStop(1, '#990000');
      this.ctx.fillStyle = endGradient;
      this.ctx.fill();
      this.ctx.strokeStyle = '#FFFFFF';
      this.ctx.lineWidth = 2;
      this.ctx.stroke();
      
      // 终点文字标记
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.font = 'bold 10px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText('E', endPoint.x, endPoint.y);
    }

    this.ctx.restore();
  }
} 