<template>
  <div class="info-panel">
    <div class="info-item">
      <span>模拟时间:</span>
      <span>{{ formatTime(simulationTime) }}</span>
    </div>
    <div class="info-item">
      <span>实际速度:</span>
      <span>{{ Math.round(currentSpeed) }} km/h</span>
    </div>
    <div class="info-item">
      <span>滑行距离:</span>
      <span>{{ Math.round(travelDistance) }} m</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InfoPanel',
  props: {
    simulationTime: Number,
    currentSpeed: Number,
    travelDistance: Number
  },
  
  methods: {
    formatTime(ms) {
      const s = Math.floor(ms / 1000);
      const m = Math.floor(s / 60);
      return `${m}:${(s % 60).toString().padStart(2, '0')}`;
    }
  }
}
</script>

<style scoped>
.info-panel {
  flex: 1;
  padding: 1rem;
  background-color: #f5f5f5;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.info-item span:first-child {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.3rem;
}

.info-item span:last-child {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}
</style> 