# Vue CLI 到 Vite 迁移指南

## 迁移概述

本项目已成功从 Vue CLI 构建系统迁移到 Vite 构建系统。以下是详细的迁移步骤和关键变更。

## 主要变更

### 1. 依赖更新

**移除的依赖：**
- `@vue/cli-service`
- `@vue/cli-plugin-babel`
- `@vue/cli-plugin-eslint`
- `@babel/core`
- `@babel/eslint-parser`

**新增的依赖：**
- `@vitejs/plugin-vue`: Vue 3 的 Vite 插件
- `vite`: Vite 构建工具

### 2. 脚本命令更新

**之前 (Vue CLI):**
```json
{
  "serve": "vue-cli-service serve",
  "build": "vue-cli-service build",
  "lint": "vue-cli-service lint"
}
```

**现在 (Vite):**
```json
{
  "dev": "vite",
  "build": "vite build",
  "preview": "vite preview",
  "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"
}
```

### 3. 配置文件变更

**删除的文件：**
- `vue.config.js`
- `babel.config.js`

**新增的文件：**
- `vite.config.js`

### 4. HTML 模板更新

- 将 `public/index.html` 移动到根目录 `index.html`
- 移除 Vue CLI 特定的模板语法（如 `<%= BASE_URL %>`）
- 添加 ES 模块入口脚本：`<script type="module" src="/src/main.js"></script>`

## 配置详情

### vite.config.js 配置

```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    port: 8080,
    open: true,
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
```

## 迁移优势

### 1. 性能提升
- **开发服务器启动速度**：从几秒钟减少到几百毫秒
- **热更新速度**：显著提升，几乎瞬时更新
- **构建速度**：使用 Rollup 进行生产构建，速度更快

### 2. 开发体验改善
- 原生 ES 模块支持
- 更好的 TypeScript 支持
- 内置的 CSS 预处理器支持
- 更简洁的配置

### 3. 现代化特性
- 基于原生 ES 模块
- 支持最新的 JavaScript 特性
- 更好的 Tree Shaking
- 内置的代码分割

## 使用方法

### 开发环境
```bash
npm run dev
```
服务器将在 http://localhost:8080 启动（如果端口被占用会自动选择其他端口）

### 生产构建
```bash
npm run build
```
构建文件将输出到 `dist` 目录

### 预览生产构建
```bash
npm run preview
```

### 代码检查
```bash
npm run lint
```

## 注意事项

1. **环境变量**：Vite 使用 `VITE_` 前缀的环境变量，而不是 `VUE_APP_`
2. **静态资源**：public 目录下的文件在构建时会被复制到 dist 根目录
3. **路径别名**：`@` 别名已配置指向 `src` 目录
4. **兼容性**：项目保持与原有代码的完全兼容性

## 可能遇到的问题

1. **端口冲突**：如果 8080 端口被占用，Vite 会自动选择其他可用端口
2. **依赖问题**：某些老旧的依赖可能需要更新以支持 ES 模块
3. **环境变量**：如果使用了环境变量，需要将 `VUE_APP_` 前缀改为 `VITE_`

## 迁移验证

✅ 开发服务器启动成功
✅ 生产构建成功
✅ 热更新功能正常
✅ 路径别名工作正常
✅ Vue 组件正常渲染
✅ 静态资源加载正常

迁移已完成，项目现在使用 Vite 作为构建工具！
