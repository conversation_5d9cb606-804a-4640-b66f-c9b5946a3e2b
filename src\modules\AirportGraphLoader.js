import graphData from '@/assets/graph.json';

export class AirportGraphLoader {
  constructor() {
    this.nodes = {};
    this.edges = {};
    this.loadGraph();
  }

  loadGraph() {
    const nodeData = graphData.nodes || graphData.graph?.nodes;
    const edgeData = graphData.edges || graphData.graph?.edges || graphData.links;
    
    if (nodeData) {
      nodeData.forEach(node => {
        if (node?.id) {
          const coords = node.pos || [node.x, node.y];
          this.nodes[node.id] = {
            id: node.id,
            x: coords[0] || node.x,
            y: coords[1] || node.y,
            specification: node.specification
          };
        }
      });
    }
    
    if (edgeData) {
      edgeData.forEach(edge => {
        if (edge?.source && edge?.target) {
          this.edges[`${edge.source}-${edge.target}`] = {
            source: edge.source,
            target: edge.target,
            length: edge.length || 1
          };
        }
      });
    }
  }
  
  transformCoordinates(nodes, canvasWidth, canvasHeight, padding = 50) {
    const coords = Object.values(nodes);
    const minLat = Math.min(...coords.map(n => n.y));
    const maxLat = Math.max(...coords.map(n => n.y));
    const minLng = Math.min(...coords.map(n => n.x));
    const maxLng = Math.max(...coords.map(n => n.x));
    
    const scale = Math.min(
      (canvasWidth - 2 * padding) / (maxLng - minLng || 0.001),
      (canvasHeight - 2 * padding) / (maxLat - minLat || 0.001)
    );
    
    const result = {};
    Object.entries(nodes).forEach(([id, node]) => {
      result[id] = {
        ...node,
        canvasX: padding + (node.x - minLng) * scale,
        canvasY: canvasHeight - padding - (node.y - minLat) * scale
      };
    });
    
    return result;
  }
} 